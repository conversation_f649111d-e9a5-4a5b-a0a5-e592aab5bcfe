"""
LLM提供商适配器模块

实现各种LLM提供商的具体适配器，包括Windmill、Google Gemini、OpenAI GPT、阿里云通义千问等。
每个适配器都实现BaseLLMProvider接口，提供统一的调用方式。
"""

import asyncio
import json
import time
import uuid
from typing import Optional, List, AsyncGenerator, Dict, Any
from loguru import logger

from .llm_service import BaseLLMProvider, LLMServiceError, RateLimitError, ValidationError
from .models import LLMRequest, LLMResponse, LLMStreamChunk, LLMUsage, LLMProviderConfig
from .windmill_client import WindmillClient
from .config import settings
from .utils import format_error_message


class WindmillLLMProvider(BaseLLMProvider):
    """Windmill LLM提供商适配器
    
    保持与现有Windmill文本生成接口的完全兼容性，
    同时提供统一的LLM服务接口。
    """
    
    def __init__(self, config: LLMProviderConfig):
        """
        初始化Windmill LLM提供商
        
        Args:
            config: 提供商配置
        """
        super().__init__(config)
        
        # 创建Windmill客户端实例
        self.client = WindmillClient(
            base_url=config.base_url or settings.windmill_base_url,
            token=config.api_key or settings.windmill_token,
            workspace=config.extra_config.get('workspace', settings.windmill_workspace)
        )
        
        # Windmill特定配置
        self.folder = config.extra_config.get('folder', settings.windmill_folder)
        self.script = config.extra_config.get('script', settings.windmill_script)
        
        logger.info(f"Windmill LLM提供商初始化完成: {config.provider_name}")
    
    async def generate_text(self, request: LLMRequest) -> LLMResponse:
        """
        生成文本（非流式）
        
        Args:
            request: LLM请求对象
            
        Returns:
            LLM响应对象
        """
        try:
            start_time = time.time()
            request_id = str(uuid.uuid4())
            
            logger.debug(f"Windmill文本生成请求开始: {request_id}")
            
            # 调用现有的Windmill客户端方法
            result = await self.client.generate_text_analysis(
                prompt=request.prompt,
                system_instruction=request.system_instruction,
                search=request.search
            )
            
            if result is None:
                raise LLMServiceError(
                    "Windmill文本生成返回空结果",
                    "EMPTY_RESPONSE",
                    self.provider_id,
                    True
                )
            
            response_time = time.time() - start_time
            
            # 构建统一的响应对象
            response = LLMResponse(
                content=result,
                model=self.config.default_model,
                provider=self.provider_id,
                request_id=request_id,
                response_time=response_time,
                finish_reason="stop",
                # Windmill不提供详细的token使用统计，使用估算值
                usage=LLMUsage(
                    prompt_tokens=self._estimate_tokens(request.prompt),
                    completion_tokens=self._estimate_tokens(result),
                    total_tokens=self._estimate_tokens(request.prompt) + self._estimate_tokens(result)
                )
            )
            
            logger.info(f"Windmill文本生成成功: {request_id}, 耗时: {response_time:.2f}秒")
            return response
            
        except Exception as e:
            logger.error(f"Windmill文本生成失败: {format_error_message(e, 'Windmill文本生成')}")
            
            # 转换为统一的异常类型
            if "timeout" in str(e).lower():
                raise LLMServiceError(
                    f"Windmill请求超时: {str(e)}",
                    "TIMEOUT_ERROR",
                    self.provider_id,
                    True
                )
            elif "rate limit" in str(e).lower():
                raise RateLimitError(
                    f"Windmill速率限制: {str(e)}",
                    self.provider_id
                )
            else:
                raise LLMServiceError(
                    f"Windmill服务错误: {str(e)}",
                    "SERVICE_ERROR",
                    self.provider_id,
                    True
                )
    
    async def generate_text_stream(self, request: LLMRequest) -> AsyncGenerator[LLMStreamChunk, None]:
        """
        生成文本（流式）
        
        注意：Windmill当前不支持流式响应，此方法将非流式响应模拟为流式输出
        
        Args:
            request: LLM请求对象
            
        Yields:
            LLM流式响应块
        """
        logger.warning("Windmill不支持真正的流式响应，使用模拟流式输出")
        
        # 获取完整响应
        response = await self.generate_text(request)
        
        # 将完整响应分块输出
        content = response.content
        chunk_size = 50  # 每块字符数
        
        for i in range(0, len(content), chunk_size):
            chunk_content = content[i:i + chunk_size]
            is_final = (i + chunk_size) >= len(content)
            
            chunk = LLMStreamChunk(
                content=content[:i + len(chunk_content)],  # 累积内容
                delta=chunk_content,  # 增量内容
                chunk_id=i // chunk_size,
                is_final=is_final
            )
            
            yield chunk
            
            # 模拟流式延迟
            if not is_final:
                await asyncio.sleep(0.1)
    
    async def validate_request(self, request: LLMRequest) -> bool:
        """
        验证请求参数
        
        Args:
            request: LLM请求对象
            
        Returns:
            验证是否通过
        """
        # 基础验证
        if not request.prompt or not request.prompt.strip():
            raise ValidationError(
                "提示词不能为空",
                self.provider_id
            )
        
        # 检查提示词长度（Windmill/Gemini的限制）
        if len(request.prompt) > 30000:  # 约30K字符限制
            raise ValidationError(
                f"提示词过长: {len(request.prompt)} 字符，最大支持 30000 字符",
                self.provider_id
            )
        
        # 检查不支持的参数
        unsupported_params = []
        if request.temperature is not None:
            unsupported_params.append("temperature")
        if request.max_tokens is not None:
            unsupported_params.append("max_tokens")
        if request.top_p is not None:
            unsupported_params.append("top_p")
        
        if unsupported_params:
            logger.warning(f"Windmill不支持以下参数，将被忽略: {', '.join(unsupported_params)}")
        
        return True
    
    def get_available_models(self) -> List[str]:
        """
        获取可用模型列表
        
        Returns:
            模型名称列表
        """
        # Windmill通过配置的脚本调用底层模型，返回配置的默认模型
        return [self.config.default_model]
    
    async def health_check(self) -> bool:
        """
        健康检查
        
        Returns:
            服务是否健康
        """
        try:
            # 发送简单的测试请求
            test_request = LLMRequest(
                prompt="测试连接",
                system_instruction="请回复'连接正常'"
            )
            
            response = await self.generate_text(test_request)
            return response is not None and len(response.content) > 0
            
        except Exception as e:
            logger.warning(f"Windmill健康检查失败: {str(e)}")
            return False
    
    def _estimate_tokens(self, text: str) -> int:
        """
        估算文本的token数量
        
        Args:
            text: 输入文本
            
        Returns:
            估算的token数量
        """
        if not text:
            return 0
        
        # 简单估算：中文按字符数，英文按单词数的1.3倍
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        english_chars = len(text) - chinese_chars
        english_words = len(text.split()) if english_chars > 0 else 0
        
        # 中文：1字符≈1token，英文：1单词≈1.3token
        estimated_tokens = chinese_chars + int(english_words * 1.3)
        
        return max(estimated_tokens, 1)


class MockLLMProvider(BaseLLMProvider):
    """模拟LLM提供商
    
    用于测试和开发环境，提供模拟的LLM响应。
    """
    
    def __init__(self, config: LLMProviderConfig):
        """初始化模拟LLM提供商"""
        super().__init__(config)
        logger.info(f"模拟LLM提供商初始化完成: {config.provider_name}")
    
    async def generate_text(self, request: LLMRequest) -> LLMResponse:
        """生成模拟文本响应"""
        # 模拟处理延迟
        await asyncio.sleep(0.5)
        
        request_id = str(uuid.uuid4())
        
        # 生成模拟响应内容
        mock_content = f"""这是一个模拟的LLM响应。

原始提示词：{request.prompt[:100]}{'...' if len(request.prompt) > 100 else ''}

模拟分析结果：
1. 技术分析：基于提供的数据，技术指标显示中性偏多的信号。
2. 基本面分析：公司基本面稳健，具有长期投资价值。
3. 风险评估：当前风险等级为中等，建议适度配置。
4. 投资建议：建议持有并关注后续发展。

注意：这是模拟响应，仅用于测试目的。"""
        
        response = LLMResponse(
            content=mock_content,
            model=self.config.default_model,
            provider=self.provider_id,
            request_id=request_id,
            response_time=0.5,
            finish_reason="stop",
            usage=LLMUsage(
                prompt_tokens=len(request.prompt) // 4,
                completion_tokens=len(mock_content) // 4,
                total_tokens=(len(request.prompt) + len(mock_content)) // 4
            )
        )
        
        return response
    
    async def generate_text_stream(self, request: LLMRequest) -> AsyncGenerator[LLMStreamChunk, None]:
        """生成模拟流式响应"""
        response = await self.generate_text(request)
        content = response.content
        
        # 分块输出
        words = content.split()
        chunk_size = 5
        
        for i in range(0, len(words), chunk_size):
            chunk_words = words[i:i + chunk_size]
            chunk_content = ' '.join(chunk_words)
            accumulated_content = ' '.join(words[:i + len(chunk_words)])
            
            is_final = (i + chunk_size) >= len(words)
            
            chunk = LLMStreamChunk(
                content=accumulated_content,
                delta=chunk_content + (' ' if not is_final else ''),
                chunk_id=i // chunk_size,
                is_final=is_final
            )
            
            yield chunk
            await asyncio.sleep(0.1)
    
    async def validate_request(self, request: LLMRequest) -> bool:
        """验证请求（模拟验证总是通过）"""
        if not request.prompt:
            raise ValidationError("提示词不能为空", self.provider_id)
        return True
    
    def get_available_models(self) -> List[str]:
        """获取可用模型列表"""
        return ["mock-model-v1", "mock-model-v2"]
    
    async def health_check(self) -> bool:
        """健康检查（模拟总是健康）"""
        return True


class LLMProviderFactory:
    """LLM提供商工厂类

    负责根据配置创建和管理不同类型的LLM提供商实例。
    支持动态注册新的提供商类型。
    """

    # 注册的提供商类型映射
    _provider_classes = {
        'windmill': WindmillLLMProvider,
        'mock': MockLLMProvider,
        # 其他提供商将在后续添加
        # 'openai': OpenAILLMProvider,
        # 'gemini': GeminiLLMProvider,
        # 'qwen': QwenLLMProvider,
    }

    @classmethod
    def register_provider_class(cls, provider_type: str, provider_class: type):
        """
        注册新的提供商类型

        Args:
            provider_type: 提供商类型标识
            provider_class: 提供商类
        """
        cls._provider_classes[provider_type] = provider_class
        logger.info(f"注册LLM提供商类型: {provider_type} -> {provider_class.__name__}")

    @classmethod
    def create_provider(cls, config: LLMProviderConfig) -> BaseLLMProvider:
        """
        创建LLM提供商实例

        Args:
            config: 提供商配置

        Returns:
            LLM提供商实例

        Raises:
            ValueError: 不支持的提供商类型
        """
        provider_type = config.provider_type.lower()

        if provider_type not in cls._provider_classes:
            raise ValueError(f"不支持的LLM提供商类型: {provider_type}")

        provider_class = cls._provider_classes[provider_type]

        try:
            provider = provider_class(config)
            logger.info(f"创建LLM提供商成功: {config.provider_name} ({config.provider_id})")
            return provider
        except Exception as e:
            logger.error(f"创建LLM提供商失败: {config.provider_name}, 错误: {str(e)}")
            raise

    @classmethod
    def create_windmill_provider(cls, provider_id: str = "windmill",
                               provider_name: str = "Windmill",
                               base_url: str = None,
                               api_key: str = None,
                               workspace: str = None,
                               folder: str = None,
                               script: str = None,
                               enabled: bool = True,
                               priority: int = 1) -> WindmillLLMProvider:
        """
        创建Windmill提供商的便捷方法

        Args:
            provider_id: 提供商ID
            provider_name: 提供商名称
            base_url: Windmill基础URL
            api_key: API密钥
            workspace: 工作空间
            folder: 文件夹
            script: 脚本名称
            enabled: 是否启用
            priority: 优先级

        Returns:
            Windmill LLM提供商实例
        """
        config = LLMProviderConfig(
            provider_id=provider_id,
            provider_name=provider_name,
            provider_type="windmill",
            base_url=base_url or settings.windmill_base_url,
            api_key=api_key or settings.windmill_token,
            default_model=settings.gemini_model,
            available_models=[settings.gemini_model],
            enabled=enabled,
            priority=priority,
            extra_config={
                'workspace': workspace or settings.windmill_workspace,
                'folder': folder or settings.windmill_folder,
                'script': script or settings.windmill_script
            }
        )

        return cls.create_provider(config)

    @classmethod
    def create_mock_provider(cls, provider_id: str = "mock",
                           provider_name: str = "Mock LLM",
                           enabled: bool = True,
                           priority: int = 999) -> MockLLMProvider:
        """
        创建模拟提供商的便捷方法

        Args:
            provider_id: 提供商ID
            provider_name: 提供商名称
            enabled: 是否启用
            priority: 优先级

        Returns:
            模拟LLM提供商实例
        """
        config = LLMProviderConfig(
            provider_id=provider_id,
            provider_name=provider_name,
            provider_type="mock",
            default_model="mock-model-v1",
            available_models=["mock-model-v1", "mock-model-v2"],
            enabled=enabled,
            priority=priority
        )

        return cls.create_provider(config)

    @classmethod
    def get_supported_types(cls) -> List[str]:
        """
        获取支持的提供商类型列表

        Returns:
            提供商类型列表
        """
        return list(cls._provider_classes.keys())

    @classmethod
    def create_from_settings(cls) -> List[BaseLLMProvider]:
        """
        根据当前设置创建默认的提供商列表

        Returns:
            提供商实例列表
        """
        providers = []

        # 创建Windmill提供商（如果配置完整）
        if settings.windmill_base_url and settings.windmill_token:
            try:
                windmill_provider = cls.create_windmill_provider()
                providers.append(windmill_provider)
                logger.info("根据设置创建Windmill提供商成功")
            except Exception as e:
                logger.warning(f"创建Windmill提供商失败: {str(e)}")

        # 如果没有可用的提供商，创建模拟提供商作为后备
        if not providers:
            mock_provider = cls.create_mock_provider()
            providers.append(mock_provider)
            logger.info("创建模拟提供商作为后备")

        return providers
