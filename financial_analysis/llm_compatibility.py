"""
LLM服务兼容性适配器

提供与现有Windmill客户端接口的完全兼容性，确保现有代码无需修改即可使用新的LLM服务层。
同时提供增强功能，如多提供商支持、故障转移、限流等。
"""

import asyncio
from typing import Optional, Dict, Any, List
from loguru import logger

from .llm_manager import unified_llm_service
from .models import LLMRequest
from .config import settings
from .utils import format_error_message


class EnhancedWindmillClient:
    """增强的Windmill客户端
    
    保持与原有WindmillClient接口的完全兼容性，
    同时在底层使用统一的LLM服务层提供增强功能。
    """
    
    def __init__(self, base_url: Optional[str] = None, token: Optional[str] = None, 
                 workspace: Optional[str] = None):
        """
        初始化增强的Windmill客户端
        
        Args:
            base_url: Windmill服务基础URL（保持兼容性，实际由LLM服务层管理）
            token: 访问令牌（保持兼容性，实际由LLM服务层管理）
            workspace: 工作空间名称（保持兼容性，实际由LLM服务层管理）
        """
        # 保存参数以保持兼容性
        self.base_url = base_url or settings.windmill_base_url
        self.token = token or settings.windmill_token
        self.workspace = workspace or settings.windmill_workspace
        
        # 使用统一LLM服务
        self.llm_service = unified_llm_service
        
        logger.info(f"增强Windmill客户端初始化完成: {self.base_url}")
    
    async def generate_text_analysis(self, prompt: str, system_instruction: str = None,
                                   search: bool = False) -> Optional[str]:
        """
        使用统一LLM服务生成文本分析（保持接口兼容性）
        
        Args:
            prompt: 分析提示词
            system_instruction: 系统指令
            search: 是否需要进行搜索
            
        Returns:
            生成的分析文本，如果失败返回None
        """
        try:
            # 使用统一LLM服务，优先使用Windmill提供商
            result = await self.llm_service.generate_text_analysis(
                prompt=prompt,
                system_instruction=system_instruction,
                search=search,
                provider_id="windmill"  # 优先使用Windmill保持兼容性
            )
            
            return result
            
        except Exception as e:
            logger.error(f"增强Windmill文本分析失败: {format_error_message(e, '增强Windmill文本分析')}")
            return None
    
    async def batch_analyze_stock_relevance(self, news_items: list, stock_info: dict) -> Optional[list]:
        """
        批量分析新闻与股票的相关性（保持接口兼容性）
        
        Args:
            news_items: 新闻条目列表
            stock_info: 股票信息
            
        Returns:
            分析结果列表
        """
        try:
            # 构建批量分析提示词
            prompt = self._build_batch_relevance_prompt(news_items, stock_info)
            
            system_instruction = """你是一个专业的金融分析师，擅长分析新闻与特定股票的相关性。
请为每条新闻评估与指定股票的相关程度，并返回JSON格式的结果。
相关性评分范围：0-100，其中：
- 90-100: 直接相关（公司公告、财报、重大事件）
- 70-89: 高度相关（行业政策、竞争对手动态）
- 50-69: 中度相关（行业趋势、宏观经济）
- 30-49: 低度相关（间接影响）
- 0-29: 无关或微弱相关"""
            
            # 使用统一LLM服务
            result = await self.llm_service.generate_text_analysis(
                prompt=prompt,
                system_instruction=system_instruction,
                search=False,
                provider_id="windmill"
            )
            
            if result:
                return self._parse_batch_relevance_result(result)
            else:
                return None
                
        except Exception as e:
            logger.error(f"批量分析股票相关性失败: {format_error_message(e, '批量分析股票相关性')}")
            return None
    
    def _build_batch_relevance_prompt(self, news_items: list, stock_info: dict) -> str:
        """构建批量相关性分析提示词"""
        stock_symbol = stock_info.get('symbol', '')
        stock_name = stock_info.get('name', '')
        stock_sector = stock_info.get('sector', '')
        
        prompt = f"""请分析以下新闻与股票 {stock_name} ({stock_symbol}) 的相关性。

股票信息：
- 代码：{stock_symbol}
- 名称：{stock_name}
- 行业：{stock_sector}

新闻列表：
"""
        
        for i, news in enumerate(news_items[:20]):  # 限制最多20条新闻
            title = news.get('title', '')
            content = news.get('content', '')[:200] if news.get('content') else ''
            source = news.get('source', '')
            
            prompt += f"""
[新闻 {i+1}]
标题：{title}
内容：{content}
来源：{source}
"""
        
        prompt += """
请为每条新闻返回JSON格式的分析结果：
{
  "results": [
    {
      "news_index": 1,
      "relevance_score": 85,
      "relevance_level": "high",
      "reason": "直接涉及该公司的财报发布",
      "category": "财报",
      "sentiment": "positive"
    }
  ]
}

要求：
1. 为每条新闻评估相关性评分（0-100）
2. 确定相关性级别：very_high(90-100), high(70-89), medium(50-69), low(30-49), very_low(0-29)
3. 简要说明相关性原因
4. 分类新闻类型：财报、公告、政策、行业、竞争、其他
5. 评估情感倾向：positive、negative、neutral
"""
        
        return prompt
    
    def _parse_batch_relevance_result(self, result: str) -> Optional[list]:
        """解析批量相关性分析结果"""
        try:
            import json
            import re
            
            # 尝试直接解析
            try:
                data = json.loads(result.strip())
                if isinstance(data, dict) and 'results' in data:
                    return data['results']
                elif isinstance(data, list):
                    return data
            except json.JSONDecodeError:
                pass
            
            # 尝试从代码块中提取
            json_pattern = r'```(?:json)?\s*\n(.*?)\n```'
            matches = re.findall(json_pattern, result, re.DOTALL | re.IGNORECASE)
            
            for match in matches:
                try:
                    data = json.loads(match.strip())
                    if isinstance(data, dict) and 'results' in data:
                        return data['results']
                    elif isinstance(data, list):
                        return data
                except json.JSONDecodeError:
                    continue
            
            return None
            
        except Exception as e:
            logger.error(f"解析批量相关性结果失败: {str(e)}")
            return None
    
    # 保持与原有接口的兼容性
    async def trigger_job(self, folder: str, script: str, payload: Dict[str, Any] = None) -> Optional[str]:
        """触发作业（兼容性方法，实际使用LLM服务）"""
        logger.warning("trigger_job方法已废弃，建议使用generate_text_analysis方法")
        
        if payload and 'prompt' in payload:
            result = await self.generate_text_analysis(
                prompt=payload['prompt'],
                system_instruction=payload.get('system_instruction'),
                search=payload.get('search', False)
            )
            return "mock-job-uuid" if result else None
        
        return None
    
    async def wait_for_job_completion(self, job_uuid: str, max_wait_time: int = 300, 
                                    poll_interval: int = 1) -> Optional[Dict[str, Any]]:
        """等待作业完成（兼容性方法）"""
        logger.warning("wait_for_job_completion方法已废弃，LLM服务已自动处理等待逻辑")
        
        # 模拟返回成功结果
        return {
            "completed": True,
            "success": True,
            "result": "LLM服务已自动处理"
        }
    
    async def execute_job(self, folder: str, script: str, payload: Dict[str, Any] = None,
                         max_wait_time: int = 300, poll_interval: int = 1) -> Optional[Dict[str, Any]]:
        """执行作业（兼容性方法，实际使用LLM服务）"""
        logger.warning("execute_job方法已废弃，建议使用generate_text_analysis方法")
        
        if payload and 'prompt' in payload:
            result = await self.generate_text_analysis(
                prompt=payload['prompt'],
                system_instruction=payload.get('system_instruction'),
                search=payload.get('search', False)
            )
            
            if result:
                return {
                    "completed": True,
                    "success": True,
                    "result": result
                }
        
        return None


# 创建增强的全局客户端实例，替换原有的windmill_client
enhanced_windmill_client = EnhancedWindmillClient()


def get_llm_service_status() -> Dict[str, Any]:
    """
    获取LLM服务状态信息
    
    Returns:
        服务状态字典
    """
    try:
        return {
            "service_available": True,
            "providers": unified_llm_service.get_available_providers(),
            "stats": unified_llm_service.get_stats(),
            "initialization_status": unified_llm_service.initializer.get_initialization_status()
        }
    except Exception as e:
        logger.error(f"获取LLM服务状态失败: {str(e)}")
        return {
            "service_available": False,
            "error": str(e)
        }


async def test_llm_service() -> Dict[str, Any]:
    """
    测试LLM服务功能
    
    Returns:
        测试结果字典
    """
    test_results = {}
    
    try:
        # 测试健康检查
        health_status = await unified_llm_service.health_check()
        test_results["health_check"] = health_status
        
        # 测试文本生成
        test_prompt = "请简单介绍一下股票投资的基本概念。"
        result = await unified_llm_service.generate_text_analysis(
            prompt=test_prompt,
            system_instruction="你是一个专业的金融顾问。"
        )
        
        test_results["text_generation"] = {
            "success": result is not None,
            "result_length": len(result) if result else 0,
            "result_preview": result[:100] + "..." if result and len(result) > 100 else result
        }
        
        # 测试兼容性接口
        compat_result = await enhanced_windmill_client.generate_text_analysis(
            prompt=test_prompt,
            system_instruction="你是一个专业的金融顾问。"
        )
        
        test_results["compatibility"] = {
            "success": compat_result is not None,
            "matches_unified_service": result == compat_result
        }
        
    except Exception as e:
        test_results["error"] = str(e)
        logger.error(f"LLM服务测试失败: {str(e)}")
    
    return test_results
