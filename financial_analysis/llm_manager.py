"""
LLM服务管理模块

负责初始化和管理所有LLM提供商，提供统一的服务入口。
支持动态配置、健康监控和故障转移。
"""

import asyncio
from typing import Optional, Dict, Any, List
from loguru import logger

from .llm_service import llm_registry, llm_manager, LLMServiceManager
from .llm_providers import LLMProviderFactory
from .models import LLMRequest, LLMResponse, LLMProviderConfig
from .config import settings


class LLMServiceInitializer:
    """LLM服务初始化器
    
    负责根据配置初始化所有LLM提供商并注册到服务注册表中。
    """
    
    def __init__(self):
        """初始化LLM服务初始化器"""
        self.initialized = False
        logger.info("LLM服务初始化器创建完成")
    
    def initialize_providers(self) -> List[str]:
        """
        初始化所有配置的LLM提供商
        
        Returns:
            成功初始化的提供商ID列表
        """
        if self.initialized:
            logger.warning("LLM服务已经初始化，跳过重复初始化")
            return list(llm_registry.get_all_providers().keys())
        
        initialized_providers = []
        
        try:
            # 1. 初始化Windmill提供商（保持向后兼容）
            if settings.windmill_enabled and settings.windmill_base_url and settings.windmill_token:
                try:
                    windmill_provider = LLMProviderFactory.create_windmill_provider(
                        provider_id="windmill",
                        provider_name="Windmill LLM服务",
                        enabled=settings.windmill_enabled,
                        priority=settings.windmill_priority
                    )
                    llm_registry.register_provider(windmill_provider)
                    initialized_providers.append("windmill")
                    logger.info("Windmill LLM提供商初始化成功")
                except Exception as e:
                    logger.error(f"Windmill LLM提供商初始化失败: {str(e)}")
            
            # 2. 初始化OpenAI提供商（预留接口）
            if settings.openai_enabled and settings.openai_api_key:
                logger.info("OpenAI提供商配置已启用，但适配器尚未实现")
                # TODO: 实现OpenAI适配器后取消注释
                # try:
                #     openai_provider = LLMProviderFactory.create_openai_provider(...)
                #     llm_registry.register_provider(openai_provider)
                #     initialized_providers.append("openai")
                # except Exception as e:
                #     logger.error(f"OpenAI LLM提供商初始化失败: {str(e)}")
            
            # 3. 初始化Google Gemini提供商（预留接口）
            if settings.gemini_enabled and settings.gemini_api_key:
                logger.info("Google Gemini提供商配置已启用，但适配器尚未实现")
                # TODO: 实现Gemini适配器后取消注释
                # try:
                #     gemini_provider = LLMProviderFactory.create_gemini_provider(...)
                #     llm_registry.register_provider(gemini_provider)
                #     initialized_providers.append("gemini")
                # except Exception as e:
                #     logger.error(f"Gemini LLM提供商初始化失败: {str(e)}")
            
            # 4. 初始化阿里云通义千问提供商（预留接口）
            if settings.qwen_enabled and settings.qwen_api_key:
                logger.info("阿里云通义千问提供商配置已启用，但适配器尚未实现")
                # TODO: 实现Qwen适配器后取消注释
                # try:
                #     qwen_provider = LLMProviderFactory.create_qwen_provider(...)
                #     llm_registry.register_provider(qwen_provider)
                #     initialized_providers.append("qwen")
                # except Exception as e:
                #     logger.error(f"通义千问LLM提供商初始化失败: {str(e)}")
            
            # 5. 如果没有可用的提供商，初始化模拟提供商作为后备
            if not initialized_providers and settings.mock_llm_enabled:
                try:
                    mock_provider = LLMProviderFactory.create_mock_provider(
                        enabled=True,
                        priority=settings.mock_llm_priority
                    )
                    llm_registry.register_provider(mock_provider)
                    initialized_providers.append("mock")
                    logger.warning("没有可用的LLM提供商，使用模拟提供商作为后备")
                except Exception as e:
                    logger.error(f"模拟LLM提供商初始化失败: {str(e)}")
            
            # 6. 设置默认提供商
            if initialized_providers:
                default_provider = settings.default_llm_provider
                if default_provider in initialized_providers:
                    llm_manager.set_default_provider(default_provider)
                else:
                    # 使用第一个可用的提供商作为默认
                    llm_manager.set_default_provider(initialized_providers[0])
                    logger.info(f"默认提供商 {default_provider} 不可用，使用 {initialized_providers[0]} 作为默认")
            
            # 7. 配置服务管理器
            llm_manager.max_retries = settings.llm_max_retries
            llm_manager.retry_delay = settings.llm_retry_delay
            
            self.initialized = True
            logger.info(f"LLM服务初始化完成，成功初始化 {len(initialized_providers)} 个提供商: {', '.join(initialized_providers)}")
            
        except Exception as e:
            logger.error(f"LLM服务初始化失败: {str(e)}")
            raise
        
        return initialized_providers
    
    def get_initialization_status(self) -> Dict[str, Any]:
        """
        获取初始化状态信息
        
        Returns:
            初始化状态字典
        """
        return {
            "initialized": self.initialized,
            "total_providers": len(llm_registry.get_all_providers()),
            "enabled_providers": len(llm_registry.get_enabled_providers()),
            "default_provider": llm_manager._default_provider_id,
            "provider_list": list(llm_registry.get_all_providers().keys())
        }


class UnifiedLLMService:
    """统一LLM服务类
    
    提供简化的LLM服务调用接口，封装底层的服务管理复杂性。
    与现有的Windmill客户端接口保持兼容。
    """
    
    def __init__(self):
        """初始化统一LLM服务"""
        self.initializer = LLMServiceInitializer()
        self._ensure_initialized()
        logger.info("统一LLM服务初始化完成")
    
    def _ensure_initialized(self):
        """确保LLM服务已初始化"""
        if not self.initializer.initialized:
            self.initializer.initialize_providers()
    
    async def generate_text_analysis(self, prompt: str, 
                                   system_instruction: str = None,
                                   search: bool = False,
                                   provider_id: str = None,
                                   **kwargs) -> Optional[str]:
        """
        生成文本分析（兼容现有接口）
        
        Args:
            prompt: 分析提示词
            system_instruction: 系统指令
            search: 是否需要进行搜索
            provider_id: 指定的提供商ID
            **kwargs: 其他参数
            
        Returns:
            生成的分析文本，如果失败返回None
        """
        try:
            self._ensure_initialized()
            
            # 构建LLM请求
            request = LLMRequest(
                prompt=prompt,
                system_instruction=system_instruction,
                search=search,
                **kwargs
            )
            
            # 调用LLM服务管理器
            response = await llm_manager.generate_text(
                request=request,
                provider_id=provider_id,
                fallback=settings.llm_fallback_enabled
            )
            
            return response.content
            
        except Exception as e:
            logger.error(f"统一LLM文本分析失败: {str(e)}")
            return None
    
    async def generate_text_with_metadata(self, prompt: str,
                                        system_instruction: str = None,
                                        search: bool = False,
                                        provider_id: str = None,
                                        **kwargs) -> Optional[LLMResponse]:
        """
        生成文本并返回完整的响应元数据
        
        Args:
            prompt: 分析提示词
            system_instruction: 系统指令
            search: 是否需要进行搜索
            provider_id: 指定的提供商ID
            **kwargs: 其他参数
            
        Returns:
            完整的LLM响应对象，如果失败返回None
        """
        try:
            self._ensure_initialized()
            
            # 构建LLM请求
            request = LLMRequest(
                prompt=prompt,
                system_instruction=system_instruction,
                search=search,
                **kwargs
            )
            
            # 调用LLM服务管理器
            response = await llm_manager.generate_text(
                request=request,
                provider_id=provider_id,
                fallback=settings.llm_fallback_enabled
            )
            
            return response
            
        except Exception as e:
            logger.error(f"统一LLM文本生成失败: {str(e)}")
            return None
    
    async def health_check(self, provider_id: str = None) -> Dict[str, bool]:
        """
        健康检查
        
        Args:
            provider_id: 指定的提供商ID，为None时检查所有提供商
            
        Returns:
            健康状态字典
        """
        self._ensure_initialized()
        return await llm_manager.health_check(provider_id)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取服务统计信息
        
        Returns:
            统计信息字典
        """
        self._ensure_initialized()
        return llm_manager.get_stats()
    
    def get_available_providers(self) -> List[str]:
        """
        获取可用的提供商列表
        
        Returns:
            提供商ID列表
        """
        self._ensure_initialized()
        return [p.provider_id for p in llm_registry.get_enabled_providers()]


# 全局统一LLM服务实例
unified_llm_service = UnifiedLLMService()
