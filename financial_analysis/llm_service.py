"""
LLM服务层核心模块

提供统一的大语言模型服务接口，支持多种LLM提供商的统一调用。
实现抽象基类、工厂模式、配置管理和错误处理机制。
"""

import asyncio
import time
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List, AsyncGenerator, Union
from loguru import logger

from .models import (
    LLMRequest, LLMResponse, LLMStreamChunk, LLMError, 
    LLMProviderConfig, LLMUsage
)
from .utils import format_error_message


class LLMServiceError(Exception):
    """LLM服务异常基类"""
    
    def __init__(self, message: str, error_code: str = "UNKNOWN_ERROR", 
                 provider: str = "unknown", is_retryable: bool = False):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.provider = provider
        self.is_retryable = is_retryable


class RateLimitError(LLMServiceError):
    """速率限制异常"""
    
    def __init__(self, message: str, provider: str, retry_after: int = 60):
        super().__init__(message, "RATE_LIMIT_EXCEEDED", provider, True)
        self.retry_after = retry_after


class ModelNotFoundError(LLMServiceError):
    """模型未找到异常"""
    
    def __init__(self, message: str, provider: str, model: str):
        super().__init__(message, "MODEL_NOT_FOUND", provider, False)
        self.model = model


class ValidationError(LLMServiceError):
    """参数验证异常"""
    
    def __init__(self, message: str, provider: str):
        super().__init__(message, "VALIDATION_ERROR", provider, False)


class BaseLLMProvider(ABC):
    """LLM提供商抽象基类
    
    定义所有LLM提供商必须实现的接口方法，确保统一的调用方式。
    """
    
    def __init__(self, config: LLMProviderConfig):
        """
        初始化LLM提供商
        
        Args:
            config: 提供商配置
        """
        self.config = config
        self.provider_id = config.provider_id
        self.provider_name = config.provider_name
        self.provider_type = config.provider_type
        
        # 统计信息
        self._request_count = 0
        self._success_count = 0
        self._error_count = 0
        self._total_tokens = 0
        
        logger.info(f"初始化LLM提供商: {self.provider_name} ({self.provider_id})")
    
    @abstractmethod
    async def generate_text(self, request: LLMRequest) -> LLMResponse:
        """
        生成文本（非流式）
        
        Args:
            request: LLM请求对象
            
        Returns:
            LLM响应对象
            
        Raises:
            LLMServiceError: 服务异常
        """
        pass
    
    @abstractmethod
    async def generate_text_stream(self, request: LLMRequest) -> AsyncGenerator[LLMStreamChunk, None]:
        """
        生成文本（流式）
        
        Args:
            request: LLM请求对象
            
        Yields:
            LLM流式响应块
            
        Raises:
            LLMServiceError: 服务异常
        """
        pass
    
    @abstractmethod
    async def validate_request(self, request: LLMRequest) -> bool:
        """
        验证请求参数
        
        Args:
            request: LLM请求对象
            
        Returns:
            验证是否通过
            
        Raises:
            ValidationError: 验证失败
        """
        pass
    
    @abstractmethod
    def get_available_models(self) -> List[str]:
        """
        获取可用模型列表
        
        Returns:
            模型名称列表
        """
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """
        健康检查
        
        Returns:
            服务是否健康
        """
        pass
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计数据字典
        """
        return {
            "provider_id": self.provider_id,
            "provider_name": self.provider_name,
            "provider_type": self.provider_type,
            "enabled": self.config.enabled,
            "request_count": self._request_count,
            "success_count": self._success_count,
            "error_count": self._error_count,
            "success_rate": self._success_count / max(self._request_count, 1),
            "total_tokens": self._total_tokens
        }
    
    def _update_stats(self, success: bool, tokens: int = 0):
        """更新统计信息"""
        self._request_count += 1
        if success:
            self._success_count += 1
        else:
            self._error_count += 1
        self._total_tokens += tokens
    
    def _create_error_response(self, error: Exception, request_id: str = None) -> LLMError:
        """创建错误响应对象"""
        if isinstance(error, LLMServiceError):
            return LLMError(
                error_code=error.error_code,
                error_message=error.message,
                error_type=error.error_code.lower(),
                provider=self.provider_id,
                request_id=request_id,
                is_retryable=error.is_retryable,
                retry_after=getattr(error, 'retry_after', None)
            )
        else:
            return LLMError(
                error_code="INTERNAL_ERROR",
                error_message=str(error),
                error_type="internal_error",
                provider=self.provider_id,
                request_id=request_id,
                is_retryable=True
            )


class LLMServiceRegistry:
    """LLM服务注册表
    
    管理所有已注册的LLM提供商，支持动态注册和查找。
    """
    
    def __init__(self):
        """初始化服务注册表"""
        self._providers: Dict[str, BaseLLMProvider] = {}
        self._configs: Dict[str, LLMProviderConfig] = {}
        logger.info("LLM服务注册表初始化完成")
    
    def register_provider(self, provider: BaseLLMProvider):
        """
        注册LLM提供商
        
        Args:
            provider: LLM提供商实例
        """
        provider_id = provider.provider_id
        self._providers[provider_id] = provider
        self._configs[provider_id] = provider.config
        
        logger.info(f"注册LLM提供商: {provider.provider_name} ({provider_id})")
    
    def unregister_provider(self, provider_id: str):
        """
        注销LLM提供商
        
        Args:
            provider_id: 提供商ID
        """
        if provider_id in self._providers:
            provider_name = self._providers[provider_id].provider_name
            del self._providers[provider_id]
            del self._configs[provider_id]
            logger.info(f"注销LLM提供商: {provider_name} ({provider_id})")
    
    def get_provider(self, provider_id: str) -> Optional[BaseLLMProvider]:
        """
        获取LLM提供商
        
        Args:
            provider_id: 提供商ID
            
        Returns:
            LLM提供商实例，如果不存在返回None
        """
        return self._providers.get(provider_id)
    
    def get_enabled_providers(self) -> List[BaseLLMProvider]:
        """
        获取所有启用的LLM提供商
        
        Returns:
            启用的提供商列表，按优先级排序
        """
        enabled_providers = [
            provider for provider in self._providers.values()
            if provider.config.enabled
        ]
        
        # 按优先级排序（数字越小优先级越高）
        enabled_providers.sort(key=lambda p: p.config.priority)
        
        return enabled_providers
    
    def get_all_providers(self) -> Dict[str, BaseLLMProvider]:
        """
        获取所有LLM提供商
        
        Returns:
            提供商字典
        """
        return self._providers.copy()
    
    def get_provider_stats(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有提供商的统计信息
        
        Returns:
            统计信息字典
        """
        return {
            provider_id: provider.get_stats()
            for provider_id, provider in self._providers.items()
        }


# 全局服务注册表实例
llm_registry = LLMServiceRegistry()


class LLMServiceManager:
    """LLM服务管理器

    提供统一的LLM服务调用接口，支持自动故障转移、负载均衡和重试机制。
    """

    def __init__(self, registry: LLMServiceRegistry = None):
        """
        初始化LLM服务管理器

        Args:
            registry: 服务注册表，默认使用全局注册表
        """
        self.registry = registry or llm_registry
        self._default_provider_id: Optional[str] = None

        # 重试配置
        self.max_retries = 3
        self.retry_delay = 1.0
        self.backoff_factor = 2.0

        logger.info("LLM服务管理器初始化完成")

    def set_default_provider(self, provider_id: str):
        """
        设置默认LLM提供商

        Args:
            provider_id: 提供商ID
        """
        if self.registry.get_provider(provider_id):
            self._default_provider_id = provider_id
            logger.info(f"设置默认LLM提供商: {provider_id}")
        else:
            logger.warning(f"提供商不存在，无法设置为默认: {provider_id}")

    async def generate_text(self, request: LLMRequest,
                          provider_id: str = None,
                          fallback: bool = True) -> LLMResponse:
        """
        生成文本（非流式）

        Args:
            request: LLM请求对象
            provider_id: 指定的提供商ID，为None时使用默认提供商
            fallback: 是否启用故障转移

        Returns:
            LLM响应对象

        Raises:
            LLMServiceError: 所有提供商都失败时抛出异常
        """
        providers = self._get_providers_for_request(provider_id, fallback)

        if not providers:
            raise LLMServiceError(
                "没有可用的LLM提供商",
                "NO_PROVIDERS_AVAILABLE"
            )

        last_error = None

        for provider in providers:
            try:
                logger.debug(f"尝试使用提供商: {provider.provider_id}")

                # 验证请求
                await provider.validate_request(request)

                # 执行请求（带重试）
                response = await self._execute_with_retry(
                    provider.generate_text, request, provider
                )

                logger.info(f"文本生成成功，提供商: {provider.provider_id}")
                return response

            except Exception as e:
                last_error = e
                logger.warning(f"提供商 {provider.provider_id} 失败: {str(e)}")

                # 如果不是可重试错误且启用了故障转移，尝试下一个提供商
                if fallback and not getattr(e, 'is_retryable', True):
                    continue

                # 如果只有一个提供商或不启用故障转移，直接抛出异常
                if not fallback or len(providers) == 1:
                    raise

        # 所有提供商都失败
        if last_error:
            raise last_error
        else:
            raise LLMServiceError(
                "所有LLM提供商都不可用",
                "ALL_PROVIDERS_FAILED"
            )

    async def generate_text_stream(self, request: LLMRequest,
                                 provider_id: str = None) -> AsyncGenerator[LLMStreamChunk, None]:
        """
        生成文本（流式）

        Args:
            request: LLM请求对象
            provider_id: 指定的提供商ID，为None时使用默认提供商

        Yields:
            LLM流式响应块

        Raises:
            LLMServiceError: 服务异常
        """
        provider = self._get_provider_for_request(provider_id)

        if not provider:
            raise LLMServiceError(
                "没有可用的LLM提供商",
                "NO_PROVIDERS_AVAILABLE"
            )

        # 验证请求
        await provider.validate_request(request)

        # 生成流式响应
        async for chunk in provider.generate_text_stream(request):
            yield chunk

    async def health_check(self, provider_id: str = None) -> Dict[str, bool]:
        """
        健康检查

        Args:
            provider_id: 指定的提供商ID，为None时检查所有提供商

        Returns:
            健康状态字典
        """
        if provider_id:
            provider = self.registry.get_provider(provider_id)
            if provider:
                return {provider_id: await provider.health_check()}
            else:
                return {provider_id: False}
        else:
            results = {}
            for provider_id, provider in self.registry.get_all_providers().items():
                try:
                    results[provider_id] = await provider.health_check()
                except Exception as e:
                    logger.warning(f"提供商 {provider_id} 健康检查失败: {str(e)}")
                    results[provider_id] = False
            return results

    def get_stats(self) -> Dict[str, Any]:
        """
        获取服务统计信息

        Returns:
            统计信息字典
        """
        return {
            "default_provider": self._default_provider_id,
            "total_providers": len(self.registry.get_all_providers()),
            "enabled_providers": len(self.registry.get_enabled_providers()),
            "provider_stats": self.registry.get_provider_stats()
        }

    def _get_providers_for_request(self, provider_id: str = None,
                                 fallback: bool = True) -> List[BaseLLMProvider]:
        """获取用于请求的提供商列表"""
        if provider_id:
            # 指定了提供商ID
            provider = self.registry.get_provider(provider_id)
            if provider and provider.config.enabled:
                providers = [provider]

                # 如果启用故障转移，添加其他可用提供商
                if fallback:
                    other_providers = [
                        p for p in self.registry.get_enabled_providers()
                        if p.provider_id != provider_id
                    ]
                    providers.extend(other_providers)

                return providers
            else:
                return []
        else:
            # 使用默认提供商或所有启用的提供商
            if self._default_provider_id:
                default_provider = self.registry.get_provider(self._default_provider_id)
                if default_provider and default_provider.config.enabled:
                    providers = [default_provider]

                    # 如果启用故障转移，添加其他可用提供商
                    if fallback:
                        other_providers = [
                            p for p in self.registry.get_enabled_providers()
                            if p.provider_id != self._default_provider_id
                        ]
                        providers.extend(other_providers)

                    return providers

            # 返回所有启用的提供商
            return self.registry.get_enabled_providers()

    def _get_provider_for_request(self, provider_id: str = None) -> Optional[BaseLLMProvider]:
        """获取用于请求的单个提供商"""
        if provider_id:
            provider = self.registry.get_provider(provider_id)
            if provider and provider.config.enabled:
                return provider
        elif self._default_provider_id:
            provider = self.registry.get_provider(self._default_provider_id)
            if provider and provider.config.enabled:
                return provider

        # 返回第一个启用的提供商
        enabled_providers = self.registry.get_enabled_providers()
        return enabled_providers[0] if enabled_providers else None

    async def _execute_with_retry(self, func, request: LLMRequest,
                                provider: BaseLLMProvider) -> LLMResponse:
        """带重试的执行函数"""
        last_error = None
        delay = self.retry_delay

        for attempt in range(self.max_retries + 1):
            try:
                start_time = time.time()
                response = await func(request)
                response_time = time.time() - start_time

                # 更新响应时间
                response.response_time = response_time

                # 更新统计信息
                tokens = response.usage.total_tokens if response.usage else 0
                provider._update_stats(True, tokens)

                return response

            except Exception as e:
                last_error = e
                provider._update_stats(False)

                # 检查是否可重试
                if not getattr(e, 'is_retryable', True) or attempt >= self.max_retries:
                    break

                # 等待后重试
                logger.debug(f"第 {attempt + 1} 次尝试失败，{delay:.1f}秒后重试: {str(e)}")
                await asyncio.sleep(delay)
                delay *= self.backoff_factor

        # 重试耗尽，抛出最后的异常
        if last_error:
            raise last_error
        else:
            raise LLMServiceError(
                "执行失败，原因未知",
                "EXECUTION_FAILED",
                provider.provider_id
            )


# 全局服务管理器实例
llm_manager = LLMServiceManager()
