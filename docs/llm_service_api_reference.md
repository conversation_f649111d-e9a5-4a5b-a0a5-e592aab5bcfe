# LLM服务层API参考文档

## 概述

本文档提供了统一LLM服务层的详细API参考，包括所有类、方法、参数和返回值的说明。

## 核心API

### UnifiedLLMService

统一LLM服务的主要入口类。

#### 方法

##### `generate_text_analysis(prompt, system_instruction=None, search=False, provider_id=None, **kwargs)`

生成文本分析（兼容现有接口）。

**参数:**
- `prompt` (str): 分析提示词，必填
- `system_instruction` (str, optional): 系统指令
- `search` (bool, optional): 是否启用搜索工具，默认False
- `provider_id` (str, optional): 指定的提供商ID，默认使用配置的默认提供商
- `**kwargs`: 其他LLM参数（temperature、max_tokens等）

**返回值:**
- `Optional[str]`: 生成的分析文本，失败时返回None

**示例:**
```python
result = await unified_llm_service.generate_text_analysis(
    prompt="请分析苹果公司的投资价值",
    system_instruction="你是专业的金融分析师",
    search=True,
    temperature=0.7
)
```

##### `generate_text_with_metadata(prompt, system_instruction=None, search=False, provider_id=None, **kwargs)`

生成文本并返回完整的响应元数据。

**参数:**
- 同 `generate_text_analysis`

**返回值:**
- `Optional[LLMResponse]`: 完整的LLM响应对象，包含内容、元数据和使用统计

**示例:**
```python
response = await unified_llm_service.generate_text_with_metadata(
    prompt="分析市场趋势",
    provider_id="windmill"
)

print(f"内容: {response.content}")
print(f"提供商: {response.provider}")
print(f"Token使用: {response.usage.total_tokens}")
```

##### `health_check(provider_id=None)`

执行健康检查。

**参数:**
- `provider_id` (str, optional): 指定的提供商ID，为None时检查所有提供商

**返回值:**
- `Dict[str, bool]`: 健康状态字典，键为提供商ID，值为健康状态

**示例:**
```python
# 检查所有提供商
health = await unified_llm_service.health_check()
# 结果: {"windmill": True, "openai": False}

# 检查特定提供商
health = await unified_llm_service.health_check("windmill")
# 结果: {"windmill": True}
```

##### `get_stats()`

获取服务统计信息。

**返回值:**
- `Dict[str, Any]`: 统计信息字典

**示例:**
```python
stats = unified_llm_service.get_stats()
# 结果包含: default_provider, total_providers, enabled_providers, provider_stats
```

##### `get_available_providers()`

获取可用的提供商列表。

**返回值:**
- `List[str]`: 提供商ID列表

## 数据模型

### LLMRequest

LLM请求模型。

**字段:**
- `prompt` (str): 提示词，必填
- `system_instruction` (Optional[str]): 系统指令
- `search` (bool): 是否启用搜索工具，默认False
- `stream` (bool): 是否启用流式响应，默认False
- `temperature` (Optional[float]): 温度参数，控制随机性 (0.0-2.0)
- `max_tokens` (Optional[int]): 最大生成token数
- `top_p` (Optional[float]): 核采样参数 (0.0-1.0)
- `top_k` (Optional[int]): Top-K采样参数
- `frequency_penalty` (Optional[float]): 频率惩罚 (-2.0-2.0)
- `presence_penalty` (Optional[float]): 存在惩罚 (-2.0-2.0)
- `extra_params` (Dict[str, Any]): 提供商特定的额外参数

### LLMResponse

LLM响应模型。

**字段:**
- `content` (str): 生成的文本内容
- `model` (str): 使用的模型名称
- `provider` (str): LLM提供商
- `request_id` (Optional[str]): 请求ID
- `usage` (Optional[LLMUsage]): 使用统计
- `created_at` (datetime): 响应创建时间
- `response_time` (Optional[float]): 响应时间（秒）
- `finish_reason` (Optional[str]): 完成原因
- `confidence_score` (Optional[float]): 置信度评分 (0.0-1.0)
- `extra_data` (Dict[str, Any]): 提供商特定的额外数据

### LLMUsage

LLM使用统计模型。

**字段:**
- `prompt_tokens` (Optional[int]): 输入token数
- `completion_tokens` (Optional[int]): 输出token数
- `total_tokens` (Optional[int]): 总token数
- `prompt_cost` (Optional[float]): 输入成本
- `completion_cost` (Optional[float]): 输出成本
- `total_cost` (Optional[float]): 总成本

### LLMProviderConfig

LLM提供商配置模型。

**字段:**
- `provider_id` (str): 提供商唯一标识
- `provider_name` (str): 提供商名称
- `provider_type` (str): 提供商类型
- `base_url` (Optional[str]): API基础URL
- `api_key` (Optional[str]): API密钥
- `default_model` (str): 默认模型名称
- `available_models` (List[str]): 可用模型列表
- `max_tokens_limit` (Optional[int]): 最大token限制
- `rate_limit_rpm` (Optional[int]): 每分钟请求限制
- `rate_limit_tpm` (Optional[int]): 每分钟token限制
- `max_retries` (int): 最大重试次数，默认3
- `retry_delay` (float): 重试延迟（秒），默认1.0
- `timeout` (int): 请求超时时间（秒），默认60
- `enabled` (bool): 是否启用，默认True
- `priority` (int): 优先级，数字越小优先级越高，默认1
- `extra_config` (Dict[str, Any]): 提供商特定的额外配置

## 提供商管理

### LLMProviderFactory

LLM提供商工厂类。

#### 类方法

##### `create_provider(config)`

创建LLM提供商实例。

**参数:**
- `config` (LLMProviderConfig): 提供商配置

**返回值:**
- `BaseLLMProvider`: LLM提供商实例

##### `create_windmill_provider(provider_id="windmill", provider_name="Windmill", ...)`

创建Windmill提供商的便捷方法。

**参数:**
- `provider_id` (str): 提供商ID，默认"windmill"
- `provider_name` (str): 提供商名称，默认"Windmill"
- `base_url` (str): Windmill基础URL
- `api_key` (str): API密钥
- `workspace` (str): 工作空间
- `folder` (str): 文件夹
- `script` (str): 脚本名称
- `enabled` (bool): 是否启用，默认True
- `priority` (int): 优先级，默认1

**返回值:**
- `WindmillLLMProvider`: Windmill LLM提供商实例

##### `create_mock_provider(provider_id="mock", provider_name="Mock LLM", ...)`

创建模拟提供商的便捷方法。

**参数:**
- `provider_id` (str): 提供商ID，默认"mock"
- `provider_name` (str): 提供商名称，默认"Mock LLM"
- `enabled` (bool): 是否启用，默认True
- `priority` (int): 优先级，默认999

**返回值:**
- `MockLLMProvider`: 模拟LLM提供商实例

##### `get_supported_types()`

获取支持的提供商类型列表。

**返回值:**
- `List[str]`: 提供商类型列表

### LLMServiceRegistry

LLM服务注册表。

#### 方法

##### `register_provider(provider)`

注册LLM提供商。

**参数:**
- `provider` (BaseLLMProvider): LLM提供商实例

##### `unregister_provider(provider_id)`

注销LLM提供商。

**参数:**
- `provider_id` (str): 提供商ID

##### `get_provider(provider_id)`

获取LLM提供商。

**参数:**
- `provider_id` (str): 提供商ID

**返回值:**
- `Optional[BaseLLMProvider]`: LLM提供商实例，如果不存在返回None

##### `get_enabled_providers()`

获取所有启用的LLM提供商。

**返回值:**
- `List[BaseLLMProvider]`: 启用的提供商列表，按优先级排序

##### `get_all_providers()`

获取所有LLM提供商。

**返回值:**
- `Dict[str, BaseLLMProvider]`: 提供商字典

## 兼容性API

### EnhancedWindmillClient

增强的Windmill客户端，保持与原有接口的完全兼容性。

#### 方法

##### `generate_text_analysis(prompt, system_instruction=None, search=False)`

使用统一LLM服务生成文本分析（保持接口兼容性）。

**参数:**
- `prompt` (str): 分析提示词
- `system_instruction` (str, optional): 系统指令
- `search` (bool, optional): 是否需要进行搜索

**返回值:**
- `Optional[str]`: 生成的分析文本，如果失败返回None

##### `batch_analyze_stock_relevance(news_items, stock_info)`

批量分析新闻与股票的相关性（保持接口兼容性）。

**参数:**
- `news_items` (list): 新闻条目列表
- `stock_info` (dict): 股票信息

**返回值:**
- `Optional[list]`: 分析结果列表

## 异常处理

### 异常类型

#### `LLMServiceError`

LLM服务异常基类。

**属性:**
- `message` (str): 错误消息
- `error_code` (str): 错误代码
- `provider` (str): 提供商
- `is_retryable` (bool): 是否可重试

#### `RateLimitError`

速率限制异常。

**属性:**
- 继承自 `LLMServiceError`
- `retry_after` (int): 建议重试间隔（秒）

#### `ModelNotFoundError`

模型未找到异常。

**属性:**
- 继承自 `LLMServiceError`
- `model` (str): 模型名称

#### `ValidationError`

参数验证异常。

**属性:**
- 继承自 `LLMServiceError`

## 工具函数

### `get_llm_service_status()`

获取LLM服务状态信息。

**返回值:**
- `Dict[str, Any]`: 服务状态字典

### `test_llm_service()`

测试LLM服务功能。

**返回值:**
- `Dict[str, Any]`: 测试结果字典

## 全局实例

### `unified_llm_service`

全局统一LLM服务实例。

### `llm_registry`

全局LLM服务注册表实例。

### `llm_manager`

全局LLM服务管理器实例。

### `enhanced_windmill_client`

全局增强Windmill客户端实例。

## 使用示例

### 基础使用

```python
from financial_analysis import unified_llm_service

# 简单文本生成
result = await unified_llm_service.generate_text_analysis(
    prompt="分析苹果公司股票",
    system_instruction="你是金融专家"
)

# 带完整元数据的生成
response = await unified_llm_service.generate_text_with_metadata(
    prompt="分析市场趋势",
    temperature=0.7,
    max_tokens=1000
)
```

### 提供商管理

```python
from financial_analysis import LLMProviderFactory, llm_registry

# 创建自定义提供商
provider = LLMProviderFactory.create_windmill_provider(
    provider_id="custom",
    base_url="https://custom.api.com",
    api_key="custom_key"
)

# 注册提供商
llm_registry.register_provider(provider)
```

### 兼容性使用

```python
from financial_analysis.llm_compatibility import enhanced_windmill_client

# 使用增强客户端（完全兼容原接口）
result = await enhanced_windmill_client.generate_text_analysis(
    prompt="分析股票",
    search=True
)
```
