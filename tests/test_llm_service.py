"""
LLM服务层单元测试

测试统一LLM服务层的各个组件，包括提供商、管理器、限流器等。
确保功能正确性和稳定性。
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from financial_analysis.models import (
    LLMRequest, LLMResponse, LLMProviderConfig, LLMUsage
)
from financial_analysis.llm_service import (
    BaseLLMProvider, LLMServiceRegistry, LLMServiceManager,
    LLMServiceError, RateLimitError, ValidationError
)
from financial_analysis.llm_providers import (
    WindmillLLMProvider, MockLLMProvider, LLMProviderFactory
)
from financial_analysis.llm_manager import (
    LLMServiceInitializer, UnifiedLLMService
)
from financial_analysis.llm_rate_limiter import (
    TokenBucket, SlidingWindowRateLimiter, LLMRateLimiter,
    CircuitBreaker, LLMServiceDegrader, RateLimitConfig
)


class TestLLMModels:
    """测试LLM数据模型"""
    
    def test_llm_request_creation(self):
        """测试LLM请求模型创建"""
        request = LLMRequest(
            prompt="测试提示词",
            system_instruction="系统指令",
            search=True,
            temperature=0.7,
            max_tokens=1000
        )
        
        assert request.prompt == "测试提示词"
        assert request.system_instruction == "系统指令"
        assert request.search is True
        assert request.temperature == 0.7
        assert request.max_tokens == 1000
        assert request.stream is False  # 默认值
    
    def test_llm_response_creation(self):
        """测试LLM响应模型创建"""
        usage = LLMUsage(
            prompt_tokens=100,
            completion_tokens=200,
            total_tokens=300
        )
        
        response = LLMResponse(
            content="生成的文本内容",
            model="test-model",
            provider="test-provider",
            usage=usage,
            finish_reason="stop"
        )
        
        assert response.content == "生成的文本内容"
        assert response.model == "test-model"
        assert response.provider == "test-provider"
        assert response.usage.total_tokens == 300
        assert response.finish_reason == "stop"
        assert isinstance(response.created_at, datetime)
    
    def test_llm_provider_config_creation(self):
        """测试LLM提供商配置模型创建"""
        config = LLMProviderConfig(
            provider_id="test-provider",
            provider_name="测试提供商",
            provider_type="test",
            default_model="test-model",
            available_models=["test-model", "test-model-2"],
            enabled=True,
            priority=1
        )
        
        assert config.provider_id == "test-provider"
        assert config.provider_name == "测试提供商"
        assert config.provider_type == "test"
        assert config.default_model == "test-model"
        assert len(config.available_models) == 2
        assert config.enabled is True
        assert config.priority == 1


class TestMockLLMProvider:
    """测试模拟LLM提供商"""
    
    @pytest.fixture
    def mock_provider(self):
        """创建模拟提供商实例"""
        config = LLMProviderConfig(
            provider_id="mock",
            provider_name="Mock Provider",
            provider_type="mock",
            default_model="mock-model",
            available_models=["mock-model"]
        )
        return MockLLMProvider(config)
    
    @pytest.mark.asyncio
    async def test_generate_text(self, mock_provider):
        """测试文本生成"""
        request = LLMRequest(prompt="测试提示词")
        response = await mock_provider.generate_text(request)
        
        assert isinstance(response, LLMResponse)
        assert response.content is not None
        assert len(response.content) > 0
        assert response.provider == "mock"
        assert response.model == "mock-model"
        assert response.usage is not None
    
    @pytest.mark.asyncio
    async def test_generate_text_stream(self, mock_provider):
        """测试流式文本生成"""
        request = LLMRequest(prompt="测试提示词")
        chunks = []
        
        async for chunk in mock_provider.generate_text_stream(request):
            chunks.append(chunk)
        
        assert len(chunks) > 0
        assert chunks[-1].is_final is True
        
        # 验证累积内容
        final_content = chunks[-1].content
        assert len(final_content) > 0
    
    @pytest.mark.asyncio
    async def test_validate_request(self, mock_provider):
        """测试请求验证"""
        # 有效请求
        valid_request = LLMRequest(prompt="有效提示词")
        assert await mock_provider.validate_request(valid_request) is True
        
        # 无效请求（空提示词）
        invalid_request = LLMRequest(prompt="")
        with pytest.raises(ValidationError):
            await mock_provider.validate_request(invalid_request)
    
    @pytest.mark.asyncio
    async def test_health_check(self, mock_provider):
        """测试健康检查"""
        result = await mock_provider.health_check()
        assert result is True
    
    def test_get_available_models(self, mock_provider):
        """测试获取可用模型"""
        models = mock_provider.get_available_models()
        assert isinstance(models, list)
        assert len(models) > 0
    
    def test_get_stats(self, mock_provider):
        """测试获取统计信息"""
        stats = mock_provider.get_stats()
        assert isinstance(stats, dict)
        assert "provider_id" in stats
        assert "request_count" in stats
        assert "success_count" in stats


class TestLLMServiceRegistry:
    """测试LLM服务注册表"""
    
    @pytest.fixture
    def registry(self):
        """创建注册表实例"""
        return LLMServiceRegistry()
    
    @pytest.fixture
    def mock_provider(self):
        """创建模拟提供商"""
        config = LLMProviderConfig(
            provider_id="test-provider",
            provider_name="Test Provider",
            provider_type="test",
            default_model="test-model",
            enabled=True,
            priority=1
        )
        return MockLLMProvider(config)
    
    def test_register_provider(self, registry, mock_provider):
        """测试注册提供商"""
        registry.register_provider(mock_provider)
        
        assert len(registry.get_all_providers()) == 1
        assert registry.get_provider("test-provider") is mock_provider
    
    def test_unregister_provider(self, registry, mock_provider):
        """测试注销提供商"""
        registry.register_provider(mock_provider)
        registry.unregister_provider("test-provider")
        
        assert len(registry.get_all_providers()) == 0
        assert registry.get_provider("test-provider") is None
    
    def test_get_enabled_providers(self, registry):
        """测试获取启用的提供商"""
        # 创建两个提供商，一个启用，一个禁用
        config1 = LLMProviderConfig(
            provider_id="enabled",
            provider_name="Enabled",
            provider_type="test",
            default_model="test",
            enabled=True,
            priority=1
        )
        config2 = LLMProviderConfig(
            provider_id="disabled",
            provider_name="Disabled",
            provider_type="test",
            default_model="test",
            enabled=False,
            priority=2
        )
        
        provider1 = MockLLMProvider(config1)
        provider2 = MockLLMProvider(config2)
        
        registry.register_provider(provider1)
        registry.register_provider(provider2)
        
        enabled_providers = registry.get_enabled_providers()
        assert len(enabled_providers) == 1
        assert enabled_providers[0].provider_id == "enabled"


class TestLLMServiceManager:
    """测试LLM服务管理器"""
    
    @pytest.fixture
    def registry_with_providers(self):
        """创建包含提供商的注册表"""
        registry = LLMServiceRegistry()
        
        # 添加两个模拟提供商
        config1 = LLMProviderConfig(
            provider_id="primary",
            provider_name="Primary",
            provider_type="mock",
            default_model="primary-model",
            enabled=True,
            priority=1
        )
        config2 = LLMProviderConfig(
            provider_id="fallback",
            provider_name="Fallback",
            provider_type="mock",
            default_model="fallback-model",
            enabled=True,
            priority=2
        )
        
        provider1 = MockLLMProvider(config1)
        provider2 = MockLLMProvider(config2)
        
        registry.register_provider(provider1)
        registry.register_provider(provider2)
        
        return registry
    
    @pytest.fixture
    def manager(self, registry_with_providers):
        """创建服务管理器"""
        manager = LLMServiceManager(registry_with_providers)
        manager.set_default_provider("primary")
        return manager
    
    @pytest.mark.asyncio
    async def test_generate_text_with_default_provider(self, manager):
        """测试使用默认提供商生成文本"""
        request = LLMRequest(prompt="测试提示词")
        response = await manager.generate_text(request)
        
        assert isinstance(response, LLMResponse)
        assert response.provider == "primary"
    
    @pytest.mark.asyncio
    async def test_generate_text_with_specific_provider(self, manager):
        """测试使用指定提供商生成文本"""
        request = LLMRequest(prompt="测试提示词")
        response = await manager.generate_text(request, provider_id="fallback")
        
        assert isinstance(response, LLMResponse)
        assert response.provider == "fallback"
    
    @pytest.mark.asyncio
    async def test_health_check(self, manager):
        """测试健康检查"""
        # 检查所有提供商
        health_status = await manager.health_check()
        assert isinstance(health_status, dict)
        assert "primary" in health_status
        assert "fallback" in health_status
        assert health_status["primary"] is True
        assert health_status["fallback"] is True
        
        # 检查特定提供商
        specific_health = await manager.health_check("primary")
        assert specific_health == {"primary": True}
    
    def test_get_stats(self, manager):
        """测试获取统计信息"""
        stats = manager.get_stats()
        assert isinstance(stats, dict)
        assert "default_provider" in stats
        assert "total_providers" in stats
        assert "enabled_providers" in stats
        assert stats["default_provider"] == "primary"


class TestTokenBucket:
    """测试令牌桶限流器"""
    
    @pytest.mark.asyncio
    async def test_acquire_tokens(self):
        """测试获取令牌"""
        bucket = TokenBucket(capacity=10, refill_rate=1.0)
        
        # 应该能够获取令牌
        assert await bucket.acquire(5) is True
        assert await bucket.acquire(5) is True
        
        # 令牌耗尽，应该失败
        assert await bucket.acquire(1) is False
    
    @pytest.mark.asyncio
    async def test_token_refill(self):
        """测试令牌补充"""
        bucket = TokenBucket(capacity=10, refill_rate=10.0)  # 每秒10个令牌
        
        # 耗尽所有令牌
        await bucket.acquire(10)
        assert await bucket.acquire(1) is False
        
        # 等待令牌补充
        await asyncio.sleep(0.2)  # 等待0.2秒，应该补充2个令牌
        assert await bucket.acquire(2) is True


class TestCircuitBreaker:
    """测试熔断器"""
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_closed_state(self):
        """测试熔断器关闭状态"""
        breaker = CircuitBreaker(failure_threshold=3)
        
        # 成功调用
        async def success_func():
            return "success"
        
        result = await breaker.call(success_func)
        assert result == "success"
        assert breaker.state == "CLOSED"
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_open_state(self):
        """测试熔断器开启状态"""
        breaker = CircuitBreaker(failure_threshold=2, recovery_timeout=1)
        
        # 失败调用
        async def failure_func():
            raise Exception("模拟失败")
        
        # 触发失败阈值
        for _ in range(2):
            with pytest.raises(Exception):
                await breaker.call(failure_func)
        
        assert breaker.state == "OPEN"
        
        # 熔断器开启时应该直接抛出异常
        with pytest.raises(LLMServiceError):
            await breaker.call(failure_func)


class TestLLMProviderFactory:
    """测试LLM提供商工厂"""
    
    def test_create_mock_provider(self):
        """测试创建模拟提供商"""
        provider = LLMProviderFactory.create_mock_provider()
        
        assert isinstance(provider, MockLLMProvider)
        assert provider.provider_id == "mock"
        assert provider.provider_type == "mock"
    
    def test_get_supported_types(self):
        """测试获取支持的提供商类型"""
        types = LLMProviderFactory.get_supported_types()
        
        assert isinstance(types, list)
        assert "mock" in types
        assert "windmill" in types
    
    def test_create_provider_with_invalid_type(self):
        """测试创建不支持的提供商类型"""
        config = LLMProviderConfig(
            provider_id="invalid",
            provider_name="Invalid",
            provider_type="invalid_type",
            default_model="test"
        )
        
        with pytest.raises(ValueError):
            LLMProviderFactory.create_provider(config)


@pytest.mark.asyncio
async def test_integration_llm_service():
    """集成测试：完整的LLM服务流程"""
    # 创建注册表和管理器
    registry = LLMServiceRegistry()
    manager = LLMServiceManager(registry)
    
    # 创建并注册模拟提供商
    provider = LLMProviderFactory.create_mock_provider()
    registry.register_provider(provider)
    manager.set_default_provider("mock")
    
    # 测试文本生成
    request = LLMRequest(
        prompt="请分析苹果公司的投资价值",
        system_instruction="你是专业的金融分析师"
    )
    
    response = await manager.generate_text(request)
    
    assert isinstance(response, LLMResponse)
    assert response.content is not None
    assert len(response.content) > 0
    assert response.provider == "mock"
    
    # 测试健康检查
    health = await manager.health_check()
    assert health["mock"] is True
    
    # 测试统计信息
    stats = manager.get_stats()
    assert stats["total_providers"] == 1
    assert stats["enabled_providers"] == 1
